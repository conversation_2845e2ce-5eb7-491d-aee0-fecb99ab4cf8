package com.example.scanlogin.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.scanlogin.R;
import com.example.scanlogin.config.ApiConfig;
import com.google.android.material.button.MaterialButton;
import java.util.List;

/**
 * 历史URL列表适配器
 */
public class HistoryUrlAdapter extends RecyclerView.Adapter<HistoryUrlAdapter.ViewHolder> {
    
    private List<ApiConfig.HistoryUrl> historyUrls;
    private OnItemActionListener listener;
    
    public interface OnItemActionListener {
        void onUseUrl(String url);
        void onDeleteUrl(String url);
    }
    
    public HistoryUrlAdapter(List<ApiConfig.HistoryUrl> historyUrls) {
        this.historyUrls = historyUrls;
    }
    
    public void setOnItemActionListener(OnItemActionListener listener) {
        this.listener = listener;
    }
    
    public void updateData(List<ApiConfig.HistoryUrl> newHistoryUrls) {
        this.historyUrls = newHistoryUrls;
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_history_url, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ApiConfig.HistoryUrl historyUrl = historyUrls.get(position);
        
        holder.tvUrl.setText(historyUrl.getUrl());
        holder.tvLastUsed.setText("最后使用: " + historyUrl.getLastUsed());
        
        // 使用按钮点击事件
        holder.btnUse.setOnClickListener(v -> {
            if (listener != null) {
                listener.onUseUrl(historyUrl.getUrl());
            }
        });
        
        // 删除按钮点击事件
        holder.btnDelete.setOnClickListener(v -> {
            if (listener != null) {
                listener.onDeleteUrl(historyUrl.getUrl());
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return historyUrls != null ? historyUrls.size() : 0;
    }
    
    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvUrl;
        TextView tvLastUsed;
        MaterialButton btnUse;
        MaterialButton btnDelete;
        
        ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvUrl = itemView.findViewById(R.id.tv_url);
            tvLastUsed = itemView.findViewById(R.id.tv_last_used);
            btnUse = itemView.findViewById(R.id.btn_use);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }
    }
}