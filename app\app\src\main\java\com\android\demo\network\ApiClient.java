package com.android.demo.network;

import android.content.Context;
import com.android.demo.BuildConfig;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.MediaType;
import com.android.demo.utils.Logger;
import com.example.scanlogin.config.ApiConfig;
import org.json.JSONObject;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.HashMap;
import java.util.Map;

public class ApiClient {
    
    private static final String DEFAULT_BASE_URL = "http://********:3001/api/"; // Android模拟器访问本机localhost
    private static Retrofit retrofit = null;
    private static Context appContext;
    private static String currentBaseUrl = null;
    private static final Map<String, Long> requestStartTimes = new HashMap<>();
    
    public static void init(Context context) {
        appContext = context.getApplicationContext();
        // 初始化时从配置中获取BASE_URL
        ApiConfig apiConfig = ApiConfig.getInstance(appContext);
        currentBaseUrl = apiConfig.getCurrentUrl();
    }
    
    /**
     * 更新BASE_URL并重新创建Retrofit实例
     */
    public static void updateBaseUrl(String newBaseUrl) {
        if (newBaseUrl != null && !newBaseUrl.equals(currentBaseUrl)) {
            currentBaseUrl = newBaseUrl;
            retrofit = null; // 重置Retrofit实例，下次调用getClient()时会重新创建
        }
    }
    
    /**
     * 获取当前BASE_URL
     */
    private static String getCurrentBaseUrl() {
        if (currentBaseUrl == null) {
            if (appContext != null) {
                ApiConfig apiConfig = ApiConfig.getInstance(appContext);
                currentBaseUrl = apiConfig.getCurrentUrl();
            } else {
                currentBaseUrl = DEFAULT_BASE_URL;
            }
        }
        return currentBaseUrl;
    }
    
    public static Retrofit getClient() {
        if (retrofit == null) {
            // 创建自定义日志拦截器
            Interceptor customLoggingInterceptor = new Interceptor() {
                @Override
                public Response intercept(Chain chain) throws IOException {
                    Request request = chain.request();
                    String requestId = null;
                    long startTime = System.currentTimeMillis();
                    
                    // 记录请求开始日志
                    if (appContext != null) {
                        Logger logger = Logger.getInstance(appContext);
                        try {
                            JSONObject requestData = Logger.createData();
                            requestData.put("url", request.url().toString());
                            requestData.put("method", request.method());
                            requestData.put("headers", request.headers().toString());
                            
                            // 记录请求体（如果有）
                            if (request.body() != null) {
                                requestData.put("hasBody", true);
                                requestData.put("contentType", request.body().contentType());
                            }
                            
                            requestId = logger.apiStart(request.method(), request.url().toString(), requestData);
                            requestStartTimes.put(requestId, startTime);
                            
                            // 将requestId添加到请求头中
                            request = request.newBuilder()
                                    .addHeader("X-Request-ID", requestId)
                                    .build();
                        } catch (Exception e) {
                            logger.error("记录API请求开始日志失败", e);
                        }
                    }
                    
                    Response response = null;
                    try {
                        // 执行请求
                        response = chain.proceed(request);
                        
                        // 记录成功响应日志
                        if (appContext != null && requestId != null) {
                            Logger logger = Logger.getInstance(appContext);
                            long duration = System.currentTimeMillis() - startTime;
                            
                            try {
                                JSONObject responseData = Logger.createData();
                                responseData.put("status", response.code());
                                responseData.put("message", response.message());
                                responseData.put("headers", response.headers().toString());
                                
                                // 读取响应体内容（需要克隆以避免消费）
                                ResponseBody responseBody = response.body();
                                if (responseBody != null) {
                                    String responseBodyString = responseBody.string();
                                    responseData.put("bodyLength", responseBodyString.length());
                                    
                                    // 重新创建响应体
                                    ResponseBody newResponseBody = ResponseBody.create(
                                            responseBodyString,
                                            responseBody.contentType()
                                    );
                                    response = response.newBuilder()
                                            .body(newResponseBody)
                                            .build();
                                }
                                
                                logger.apiSuccess(requestId, request.method(), request.url().toString(), responseData, duration);
                            } catch (Exception e) {
                                logger.error("记录API成功响应日志失败", e);
                            }
                            
                            requestStartTimes.remove(requestId);
                        }
                        
                        return response;
                        
                    } catch (IOException e) {
                        // 记录失败日志
                        if (appContext != null && requestId != null) {
                            Logger logger = Logger.getInstance(appContext);
                            Long startTimeObj = requestStartTimes.get(requestId);
                            long duration = startTimeObj != null ? System.currentTimeMillis() - startTimeObj : 0;
                            
                            logger.apiError(requestId, request.method(), request.url().toString(), e, duration);
                            requestStartTimes.remove(requestId);
                        }
                        throw e;
                    }
                }
            };
            
            // 创建标准日志拦截器（仅在Debug模式下）
            HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
            logging.setLevel(HttpLoggingInterceptor.Level.BODY);
            
            // 创建OkHttpClient
            OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder()
                    .addInterceptor(customLoggingInterceptor)
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS);
            
            // 仅在Debug模式下添加标准日志拦截器
            if (BuildConfig.DEBUG) {
                clientBuilder.addInterceptor(logging);
            }
            
            OkHttpClient client = clientBuilder.build();
            
            // 创建Retrofit实例
            retrofit = new Retrofit.Builder()
                    .baseUrl(getCurrentBaseUrl())
                    .client(client)
                    .addConverterFactory(GsonConverterFactory.create())
                    .build();
        }
        return retrofit;
    }
}