# Android 项目版本信息

## Android 版本要求

- **编译 SDK 版本**: 36
- **目标 SDK 版本**: 36
- **最低 SDK 版本**: 31
- **Java 版本**: Java 17 (sourceCompatibility & targetCompatibility)

## 构建工具版本

### Gradle 相关
- **Gradle 版本**: 8.13
- **Android Gradle Plugin (AGP)**: 8.11.1
- **Gradle 分发地址**: https://mirrors.huaweicloud.com/gradle/gradle-8.13-bin.zip

### JVM 配置
- **JVM 参数**: -Xmx2048m -Dfile.encoding=UTF-8
- **Gradle Java Home**: /Users/<USER>/Library/Java/JavaVirtualMachines/jbr-17.0.9/Contents/Home
- **AndroidX**: 启用 (android.useAndroidX=true)
- **非传递性 R 类**: 启用 (android.nonTransitiveRClass=true)

## 依赖库版本

### 核心库
- **AppCompat**: 1.7.1
- **Material Design**: 1.12.0

### 测试库
- **JUnit**: 4.13.2
- **AndroidX JUnit**: 1.2.1
- **Espresso Core**: 3.6.1

## 构建配置

### 构建类型
- **Release**: 
  - 代码混淆: 禁用 (minifyEnabled false)
  - ProGuard 文件: proguard-android-optimize.txt, proguard-rules.pro

### 仓库配置
- **Google Maven**: 启用
- **Maven Central**: 启用
- **Gradle Plugin Portal**: 启用

## 项目结构

```
loadhtml-android-demo/
├── app/                    # 主应用模块
│   ├── build.gradle       # 应用级构建配置
│   └── src/               # 源代码目录
├── build.gradle           # 项目级构建配置
├── gradle.properties      # Gradle 属性配置
├── gradle/
│   ├── libs.versions.toml # 版本目录管理
│   └── wrapper/           # Gradle Wrapper
└── settings.gradle        # 项目设置
```

## 版本兼容性说明

- 此项目使用较新的 Android API 级别 (SDK 36)，对应 Android 15
- 最低支持 Android API 31，对应 Android 12
- 使用 Java 17 作为编译和目标版本（AGP 8.11.1要求）
- 启用了 AndroidX 支持库
- 使用版本目录 (libs.versions.toml) 进行依赖版本管理

---

*文档生成时间: $(date)*
*项目路径: /Users/<USER>/WK/devAPP/Android/loadhtml-android-demo*