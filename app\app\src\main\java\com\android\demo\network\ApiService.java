package com.android.demo.network;

import com.android.demo.model.LoginRequest;
import com.android.demo.model.LoginResponse;
import com.android.demo.model.QrConfirmRequest;
import com.android.demo.model.ApiResponse;
import com.android.demo.model.User;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;

public interface ApiService {
    
    /**
     * 用户登录
     */
    @POST("login")
    Call<LoginResponse> login(@Body LoginRequest request);
    
    /**
     * 确认二维码扫描
     */
    @POST("qr/confirm")
    Call<ApiResponse> confirmQrCode(@Body QrConfirmRequest request);
    
    /**
     * 获取用户信息
     */
    @GET("user/info")
    Call<User> getUserInfo(@Header("Authorization") String token);
}