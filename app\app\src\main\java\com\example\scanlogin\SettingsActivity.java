package com.example.scanlogin;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.example.scanlogin.adapter.HistoryUrlAdapter;
import com.example.scanlogin.config.ApiConfig;
import com.example.scanlogin.network.ApiClient;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.util.List;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 设置页面Activity
 * 用于管理API地址配置和历史记录
 */
public class SettingsActivity extends AppCompatActivity implements HistoryUrlAdapter.OnItemActionListener {
    
    private MaterialToolbar toolbar;
    private TextView tvCurrentUrl;
    private TextInputEditText etApiUrl;
    private MaterialButton btnTestConnection;
    private MaterialButton btnSaveUrl;
    private MaterialButton btnClearHistory;
    private MaterialButton btnUseLocal;
    private MaterialButton btnUseProd;
    private RecyclerView rvHistoryUrls;
    private TextView tvEmptyHistory;
    
    private ApiConfig apiConfig;
    private HistoryUrlAdapter historyAdapter;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);
        
        initViews();
        initData();
        setupListeners();
        loadHistoryUrls();
    }
    
    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        tvCurrentUrl = findViewById(R.id.tv_current_url);
        etApiUrl = findViewById(R.id.et_api_url);
        btnTestConnection = findViewById(R.id.btn_test_connection);
        btnSaveUrl = findViewById(R.id.btn_save_url);
        btnClearHistory = findViewById(R.id.btn_clear_history);
        btnUseLocal = findViewById(R.id.btn_use_local);
        btnUseProd = findViewById(R.id.btn_use_prod);
        rvHistoryUrls = findViewById(R.id.rv_history_urls);
        tvEmptyHistory = findViewById(R.id.tv_empty_history);
        
        // 设置工具栏
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        // 设置RecyclerView
        rvHistoryUrls.setLayoutManager(new LinearLayoutManager(this));
    }
    
    private void initData() {
        apiConfig = ApiConfig.getInstance(this);
        
        // 显示当前API地址
        tvCurrentUrl.setText(apiConfig.getCurrentUrl());
        
        // 初始化历史记录适配器
        historyAdapter = new HistoryUrlAdapter(apiConfig.getHistoryUrls());
        historyAdapter.setOnItemActionListener(this);
        rvHistoryUrls.setAdapter(historyAdapter);
    }
    
    private void setupListeners() {
        // 工具栏返回按钮
        toolbar.setNavigationOnClickListener(v -> finish());
        
        // 测试连接按钮
        btnTestConnection.setOnClickListener(v -> testConnection());
        
        // 保存地址按钮
        btnSaveUrl.setOnClickListener(v -> saveApiUrl());
        
        // 清空历史按钮
        btnClearHistory.setOnClickListener(v -> showClearHistoryDialog());
        
        // 使用本地地址按钮
        btnUseLocal.setOnClickListener(v -> usePresetUrl("http://10.0.2.2:3001/api/"));
        
        // 使用生产地址按钮
        btnUseProd.setOnClickListener(v -> usePresetUrl("https://api.example.com/api/"));
    }
    
    /**
     * 测试连接
     */
    private void testConnection() {
        String url = etApiUrl.getText().toString().trim();
        if (TextUtils.isEmpty(url)) {
            Toast.makeText(this, "请输入API地址", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (!apiConfig.isValidUrl(url)) {
            Toast.makeText(this, "请输入有效的URL地址", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 格式化URL
        url = apiConfig.formatUrl(url);
        
        btnTestConnection.setEnabled(false);
        btnTestConnection.setText("测试中...");
        
        // 创建测试请求
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .build();
        
        Request request = new Request.Builder()
                .url(url + "health") // 假设有健康检查接口
                .build();
        
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                runOnUiThread(() -> {
                    btnTestConnection.setEnabled(true);
                    btnTestConnection.setText("测试连接");
                    Toast.makeText(SettingsActivity.this, "连接失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                runOnUiThread(() -> {
                    btnTestConnection.setEnabled(true);
                    btnTestConnection.setText("测试连接");
                    
                    if (response.isSuccessful()) {
                        Toast.makeText(SettingsActivity.this, "连接成功！", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(SettingsActivity.this, "连接失败，状态码: " + response.code(), Toast.LENGTH_SHORT).show();
                    }
                });
                response.close();
            }
        });
    }
    
    /**
     * 保存API地址
     */
    private void saveApiUrl() {
        String url = etApiUrl.getText().toString().trim();
        if (TextUtils.isEmpty(url)) {
            Toast.makeText(this, "请输入API地址", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (!apiConfig.isValidUrl(url)) {
            Toast.makeText(this, "请输入有效的URL地址", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 格式化并保存URL
        url = apiConfig.formatUrl(url);
        apiConfig.setCurrentUrl(url);
        
        // 更新显示
        tvCurrentUrl.setText(url);
        etApiUrl.setText("");
        
        // 重新初始化ApiClient
        ApiClient.updateBaseUrl(url);
        
        // 刷新历史记录
        loadHistoryUrls();
        
        Toast.makeText(this, "API地址已保存", Toast.LENGTH_SHORT).show();
    }
    
    /**
     * 使用预设地址
     */
    private void usePresetUrl(String url) {
        apiConfig.setCurrentUrl(url);
        tvCurrentUrl.setText(url);
        
        // 重新初始化ApiClient
        ApiClient.updateBaseUrl(url);
        
        // 刷新历史记录
        loadHistoryUrls();
        
        Toast.makeText(this, "已切换到: " + url, Toast.LENGTH_SHORT).show();
    }
    
    /**
     * 显示清空历史记录确认对话框
     */
    private void showClearHistoryDialog() {
        new AlertDialog.Builder(this)
                .setTitle("清空历史记录")
                .setMessage("确定要清空所有历史地址吗？此操作不可撤销。")
                .setPositiveButton("确定", (dialog, which) -> {
                    apiConfig.clearHistory();
                    loadHistoryUrls();
                    Toast.makeText(this, "历史记录已清空", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    /**
     * 加载历史记录
     */
    private void loadHistoryUrls() {
        List<ApiConfig.HistoryUrl> historyUrls = apiConfig.getHistoryUrls();
        historyAdapter.updateData(historyUrls);
        
        // 显示/隐藏空状态
        if (historyUrls.isEmpty()) {
            tvEmptyHistory.setVisibility(View.VISIBLE);
            rvHistoryUrls.setVisibility(View.GONE);
        } else {
            tvEmptyHistory.setVisibility(View.GONE);
            rvHistoryUrls.setVisibility(View.VISIBLE);
        }
    }
    
    @Override
    public void onUseUrl(String url) {
        apiConfig.setCurrentUrl(url);
        tvCurrentUrl.setText(url);
        
        // 重新初始化ApiClient
        ApiClient.updateBaseUrl(url);
        
        // 刷新历史记录（更新最后使用时间）
        loadHistoryUrls();
        
        Toast.makeText(this, "已切换到: " + url, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onDeleteUrl(String url) {
        new AlertDialog.Builder(this)
                .setTitle("删除地址")
                .setMessage("确定要删除这个地址吗？")
                .setPositiveButton("确定", (dialog, which) -> {
                    apiConfig.removeFromHistory(url);
                    loadHistoryUrls();
                    Toast.makeText(this, "地址已删除", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("取消", null)
                .show();
    }
}