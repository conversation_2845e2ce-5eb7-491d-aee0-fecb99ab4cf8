package com.example.scanlogin.config;

import android.content.Context;
import android.content.SharedPreferences;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * API配置管理类
 * 负责管理API地址的保存、读取和历史记录
 */
public class ApiConfig {
    private static final String PREFS_NAME = "api_config";
    private static final String KEY_CURRENT_URL = "current_url";
    private static final String KEY_HISTORY_URLS = "history_urls";
    private static final String DEFAULT_URL = "http://10.0.2.2:3001/api/";
    
    private static ApiConfig instance;
    private SharedPreferences prefs;
    private Gson gson;
    
    private ApiConfig(Context context) {
        prefs = context.getApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        gson = new Gson();
    }
    
    public static synchronized ApiConfig getInstance(Context context) {
        if (instance == null) {
            instance = new ApiConfig(context);
        }
        return instance;
    }
    
    /**
     * 获取当前API地址
     */
    public String getCurrentUrl() {
        return prefs.getString(KEY_CURRENT_URL, DEFAULT_URL);
    }
    
    /**
     * 设置当前API地址
     */
    public void setCurrentUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return;
        }
        
        // 确保URL以/结尾
        if (!url.endsWith("/")) {
            url = url + "/";
        }
        
        prefs.edit().putString(KEY_CURRENT_URL, url).apply();
        
        // 添加到历史记录
        addToHistory(url);
    }
    
    /**
     * 获取历史地址列表
     */
    public List<HistoryUrl> getHistoryUrls() {
        String json = prefs.getString(KEY_HISTORY_URLS, "");
        if (json.isEmpty()) {
            return new ArrayList<>();
        }
        
        Type type = new TypeToken<List<HistoryUrl>>(){}.getType();
        List<HistoryUrl> historyUrls = gson.fromJson(json, type);
        return historyUrls != null ? historyUrls : new ArrayList<>();
    }
    
    /**
     * 添加地址到历史记录
     */
    private void addToHistory(String url) {
        List<HistoryUrl> historyUrls = getHistoryUrls();
        
        // 移除已存在的相同URL
        historyUrls.removeIf(item -> item.getUrl().equals(url));
        
        // 添加到列表开头
        HistoryUrl newItem = new HistoryUrl(url, getCurrentTimeString());
        historyUrls.add(0, newItem);
        
        // 限制历史记录数量（最多保存10个）
        if (historyUrls.size() > 10) {
            historyUrls = historyUrls.subList(0, 10);
        }
        
        // 保存到SharedPreferences
        String json = gson.toJson(historyUrls);
        prefs.edit().putString(KEY_HISTORY_URLS, json).apply();
    }
    
    /**
     * 删除历史记录中的指定URL
     */
    public void removeFromHistory(String url) {
        List<HistoryUrl> historyUrls = getHistoryUrls();
        historyUrls.removeIf(item -> item.getUrl().equals(url));
        
        String json = gson.toJson(historyUrls);
        prefs.edit().putString(KEY_HISTORY_URLS, json).apply();
    }
    
    /**
     * 清空历史记录
     */
    public void clearHistory() {
        prefs.edit().remove(KEY_HISTORY_URLS).apply();
    }
    
    /**
     * 验证URL格式
     */
    public boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        url = url.trim();
        return url.startsWith("http://") || url.startsWith("https://");
    }
    
    /**
     * 格式化URL
     */
    public String formatUrl(String url) {
        if (url == null) {
            return "";
        }
        
        url = url.trim();
        if (!url.endsWith("/")) {
            url = url + "/";
        }
        
        return url;
    }
    
    /**
     * 获取当前时间字符串
     */
    private String getCurrentTimeString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    /**
     * 获取预设地址列表
     */
    public List<PresetUrl> getPresetUrls() {
        List<PresetUrl> presets = new ArrayList<>();
        presets.add(new PresetUrl("本地开发环境", "http://10.0.2.2:3001/api/", "适用于Android模拟器访问本地服务器"));
        presets.add(new PresetUrl("生产环境", "https://api.example.com/api/", "正式生产环境地址"));
        return presets;
    }
    
    /**
     * 历史URL数据类
     */
    public static class HistoryUrl {
        private String url;
        private String lastUsed;
        
        public HistoryUrl(String url, String lastUsed) {
            this.url = url;
            this.lastUsed = lastUsed;
        }
        
        public String getUrl() {
            return url;
        }
        
        public void setUrl(String url) {
            this.url = url;
        }
        
        public String getLastUsed() {
            return lastUsed;
        }
        
        public void setLastUsed(String lastUsed) {
            this.lastUsed = lastUsed;
        }
    }
    
    /**
     * 预设URL数据类
     */
    public static class PresetUrl {
        private String name;
        private String url;
        private String description;
        
        public PresetUrl(String name, String url, String description) {
            this.name = name;
            this.url = url;
            this.description = description;
        }
        
        public String getName() {
            return name;
        }
        
        public String getUrl() {
            return url;
        }
        
        public String getDescription() {
            return description;
        }
    }
}