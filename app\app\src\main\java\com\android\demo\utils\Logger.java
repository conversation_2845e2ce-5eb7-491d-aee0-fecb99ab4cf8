package com.android.demo.utils;

import android.content.Context;
import android.util.Log;
import com.android.demo.BuildConfig;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.UUID;

/**
 * Android日志工具类
 * 提供统一的日志记录功能，支持不同级别的日志输出和文件存储
 */
public class Logger {
    
    private static final String TAG = "ScanLogin";
    private static final String LOG_FILE_NAME = "app_logs.txt";
    private static Logger instance;
    private Context context;
    private boolean isDebugMode;
    private SimpleDateFormat dateFormat;
    private File logFile;
    
    // 日志级别
    public enum Level {
        DEBUG(0, "DEBUG"),
        INFO(1, "INFO"),
        WARN(2, "WARN"),
        ERROR(3, "ERROR");
        
        private final int value;
        private final String name;
        
        Level(int value, String name) {
            this.value = value;
            this.name = name;
        }
        
        public int getValue() {
            return value;
        }
        
        public String getName() {
            return name;
        }
    }
    
    private Logger(Context context) {
        this.context = context.getApplicationContext();
        this.isDebugMode = BuildConfig.DEBUG;
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault());
        initLogFile();
    }
    
    public static synchronized Logger getInstance(Context context) {
        if (instance == null) {
            instance = new Logger(context);
        }
        return instance;
    }
    
    public static Logger getInstance() {
        if (instance == null) {
            throw new IllegalStateException("Logger not initialized. Call getInstance(Context) first.");
        }
        return instance;
    }
    
    private void initLogFile() {
        try {
            File logDir = new File(context.getFilesDir(), "logs");
            if (!logDir.exists()) {
                logDir.mkdirs();
            }
            logFile = new File(logDir, LOG_FILE_NAME);
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize log file", e);
        }
    }
    
    /**
     * 生成请求ID
     */
    public String generateRequestId() {
        return "req_" + UUID.randomUUID().toString().substring(0, 8) + "_" + System.currentTimeMillis();
    }
    
    /**
     * Debug级别日志
     */
    public void debug(String message) {
        debug(message, null);
    }
    
    public void debug(String message, JSONObject data) {
        log(Level.DEBUG, message, data, null);
    }
    
    /**
     * Info级别日志
     */
    public void info(String message) {
        info(message, null);
    }
    
    public void info(String message, JSONObject data) {
        log(Level.INFO, message, data, null);
    }
    
    /**
     * Warning级别日志
     */
    public void warn(String message) {
        warn(message, null);
    }
    
    public void warn(String message, JSONObject data) {
        log(Level.WARN, message, data, null);
    }
    
    /**
     * Error级别日志
     */
    public void error(String message) {
        error(message, null, null);
    }
    
    public void error(String message, Throwable throwable) {
        error(message, throwable, null);
    }
    
    public void error(String message, Throwable throwable, JSONObject data) {
        log(Level.ERROR, message, data, throwable);
    }
    
    /**
     * API请求开始日志
     */
    public String apiStart(String method, String url, JSONObject requestData) {
        String requestId = generateRequestId();
        try {
            JSONObject logData = new JSONObject();
            logData.put("requestId", requestId);
            logData.put("method", method.toUpperCase());
            logData.put("url", url);
            logData.put("requestData", requestData);
            logData.put("timestamp", System.currentTimeMillis());
            
            info("API请求开始", logData);
        } catch (JSONException e) {
            error("记录API开始日志失败", e);
        }
        return requestId;
    }
    
    /**
     * API请求成功日志
     */
    public void apiSuccess(String requestId, String method, String url, JSONObject responseData, long duration) {
        try {
            JSONObject logData = new JSONObject();
            logData.put("requestId", requestId);
            logData.put("method", method.toUpperCase());
            logData.put("url", url);
            logData.put("responseData", responseData);
            logData.put("duration", duration + "ms");
            logData.put("status", "success");
            
            info("API请求成功", logData);
        } catch (JSONException e) {
            error("记录API成功日志失败", e);
        }
    }
    
    /**
     * API请求失败日志
     */
    public void apiError(String requestId, String method, String url, Throwable error, long duration) {
        try {
            JSONObject logData = new JSONObject();
            logData.put("requestId", requestId);
            logData.put("method", method.toUpperCase());
            logData.put("url", url);
            logData.put("duration", duration + "ms");
            logData.put("status", "error");
            logData.put("errorMessage", error.getMessage());
            
            error("API请求失败", error, logData);
        } catch (JSONException e) {
            error("记录API错误日志失败", e);
        }
    }
    
    /**
     * 用户操作日志
     */
    public void userAction(String action, JSONObject data) {
        try {
            JSONObject logData = new JSONObject();
            logData.put("action", action);
            logData.put("data", data);
            logData.put("userId", getCurrentUserId());
            logData.put("timestamp", System.currentTimeMillis());
            
            info("用户操作", logData);
        } catch (JSONException e) {
            error("记录用户操作日志失败", e);
        }
    }
    
    /**
     * 页面访问日志
     */
    public void pageView(String activityName) {
        try {
            JSONObject logData = new JSONObject();
            logData.put("activityName", activityName);
            logData.put("userId", getCurrentUserId());
            logData.put("timestamp", System.currentTimeMillis());
            
            info("页面访问", logData);
        } catch (JSONException e) {
            error("记录页面访问日志失败", e);
        }
    }
    
    /**
     * 扫码事件日志
     */
    public void scanEvent(String event, JSONObject data) {
        try {
            JSONObject logData = new JSONObject();
            logData.put("event", event);
            logData.put("data", data);
            logData.put("timestamp", System.currentTimeMillis());
            
            info("扫码事件", logData);
        } catch (JSONException e) {
            error("记录扫码事件日志失败", e);
        }
    }
    
    /**
     * 权限事件日志
     */
    public void permissionEvent(String permission, boolean granted) {
        try {
            JSONObject logData = new JSONObject();
            logData.put("permission", permission);
            logData.put("granted", granted);
            logData.put("timestamp", System.currentTimeMillis());
            
            info("权限事件", logData);
        } catch (JSONException e) {
            error("记录权限事件日志失败", e);
        }
    }
    
    /**
     * 核心日志记录方法
     */
    private void log(Level level, String message, JSONObject data, Throwable throwable) {
        String timestamp = dateFormat.format(new Date());
        String logMessage = String.format("[%s] %s - %s", level.getName(), timestamp, message);
        
        // 添加数据信息
        if (data != null) {
            logMessage += " | Data: " + data.toString();
        }
        
        // 控制台输出
        switch (level) {
            case DEBUG:
                if (isDebugMode) {
                    Log.d(TAG, logMessage, throwable);
                }
                break;
            case INFO:
                Log.i(TAG, logMessage, throwable);
                break;
            case WARN:
                Log.w(TAG, logMessage, throwable);
                break;
            case ERROR:
                Log.e(TAG, logMessage, throwable);
                break;
        }
        
        // 写入文件（仅在Debug模式下）
        if (isDebugMode) {
            writeToFile(logMessage, throwable);
        }
    }
    
    /**
     * 写入日志文件
     */
    private void writeToFile(String message, Throwable throwable) {
        if (logFile == null) return;
        
        try (FileWriter writer = new FileWriter(logFile, true)) {
            writer.write(message + "\n");
            if (throwable != null) {
                writer.write("Exception: " + Log.getStackTraceString(throwable) + "\n");
            }
            writer.write("\n");
            writer.flush();
        } catch (IOException e) {
            Log.e(TAG, "Failed to write log to file", e);
        }
    }
    
    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            PreferenceManager preferenceManager = new PreferenceManager(context);
            if (preferenceManager.isLoggedIn()) {
                return String.valueOf(preferenceManager.getUser().getId());
            }
        } catch (Exception e) {
            // 忽略异常，返回默认值
        }
        return "anonymous";
    }
    
    /**
     * 清空日志文件
     */
    public void clearLogFile() {
        if (logFile != null && logFile.exists()) {
            try {
                logFile.delete();
                initLogFile();
                info("日志文件已清空");
            } catch (Exception e) {
                error("清空日志文件失败", e);
            }
        }
    }
    
    /**
     * 获取日志文件路径
     */
    public String getLogFilePath() {
        return logFile != null ? logFile.getAbsolutePath() : null;
    }
    
    /**
     * 创建JSON对象的便捷方法
     */
    public static JSONObject createData() {
        return new JSONObject();
    }
    
    public static JSONObject createData(String key, Object value) {
        try {
            JSONObject data = new JSONObject();
            data.put(key, value);
            return data;
        } catch (JSONException e) {
            Log.e(TAG, "Failed to create JSON data", e);
            return new JSONObject();
        }
    }
}