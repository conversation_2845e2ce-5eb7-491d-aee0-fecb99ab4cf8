#!/bin/bash

# Android项目构建修复脚本
# 解决JDK兼容性问题

echo "设置Java 11环境..."
export JAVA_HOME=/Library/Java/JavaVirtualMachines/jdk-11.0.1.jdk/Contents/Home

echo "当前Java版本:"
$JAVA_HOME/bin/java -version

echo "\n开始清理项目..."
./gradlew clean

echo "\n开始构建项目..."
./gradlew build

echo "\n构建完成！"
echo "如果在Android Studio中遇到问题，请确保:"
echo "1. File -> Project Structure -> SDK Location -> JDK location 设置为: $JAVA_HOME"
echo "2. 或者在Android Studio的Terminal中运行此脚本"