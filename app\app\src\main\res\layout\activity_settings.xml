<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <!-- 顶部工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/design_default_color_primary"
        app:title="设置"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- API地址配置卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="API服务器地址"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="16dp" />

                    <!-- 当前地址显示 -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="当前地址:"
                        android:textSize="14sp"
                        android:textColor="@color/design_default_color_on_surface"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tv_current_url"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="http://10.0.2.2:3001/api/"
                        android:textSize="14sp"
                        android:textColor="@color/design_default_color_primary"
                        android:textStyle="bold"
                        android:layout_marginBottom="20dp" />

                    <!-- 新地址输入 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="输入新的API地址"
                        app:boxStrokeColor="@color/design_default_color_primary"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_api_url"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textUri"
                            android:textSize="14sp"
                            android:textColorHint="@color/design_default_color_primary" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 操作按钮 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="end">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_test_connection"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="测试连接"
                            android:layout_marginEnd="8dp" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_save_url"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="保存地址" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 历史地址卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="历史地址"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/black" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_clear_history"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="清空历史"
                            android:textColor="@color/design_default_color_error" />

                    </LinearLayout>

                    <!-- 历史地址列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_history_urls"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                    <!-- 空状态提示 -->
                    <TextView
                        android:id="@+id/tv_empty_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="暂无历史地址"
                        android:textSize="14sp"
                        android:textColor="@color/design_default_color_on_surface"
                        android:gravity="center"
                        android:padding="20dp"
                        android:visibility="gone" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 预设地址卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="预设地址"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_marginBottom="16dp" />

                    <!-- 本地开发地址 -->
                    <LinearLayout
                        android:id="@+id/ll_preset_local"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:layout_marginBottom="8dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="本地开发环境"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="http://10.0.2.2:3001/api/"
                                android:textSize="12sp"
                                android:textColor="@color/design_default_color_on_surface" />

                        </LinearLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_use_local"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="使用" />

                    </LinearLayout>

                    <!-- 生产环境地址 -->
                    <LinearLayout
                        android:id="@+id/ll_preset_prod"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="生产环境"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@color/black" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="https://api.example.com/api/"
                                android:textSize="12sp"
                                android:textColor="@color/design_default_color_on_surface" />

                        </LinearLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_use_prod"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="使用" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>