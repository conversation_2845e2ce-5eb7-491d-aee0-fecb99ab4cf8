<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- 扫码视图 -->
    <com.journeyapps.barcodescanner.DecoratedBarcodeView
        android:id="@+id/barcode_scanner"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:zxing_scanner_layout="@layout/custom_barcode_scanner" />

    <!-- 顶部工具栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="@color/black_overlay">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_revert"
            app:tint="@color/white"
            android:contentDescription="返回" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="扫描二维码"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:gravity="center" />

        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <!-- 底部提示信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center"
        android:background="@color/black_overlay">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="将二维码放入框内，即可自动扫描"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="请扫描网页端登录页面的二维码"
            android:textSize="14sp"
            android:textColor="@color/white_70"
            android:gravity="center" />

    </LinearLayout>

</RelativeLayout>