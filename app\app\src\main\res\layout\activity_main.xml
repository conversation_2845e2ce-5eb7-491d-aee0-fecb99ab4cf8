<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/white">

    <!-- 顶部工具栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingVertical="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="扫码登录APP"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/black" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_settings"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置"
            android:layout_marginEnd="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_logout"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="退出登录"
            android:textColor="@color/design_default_color_error" />

    </LinearLayout>

    <!-- 用户信息卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        android:layout_marginBottom="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:id="@+id/tv_welcome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="欢迎回来！"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/tv_username"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="用户名: "
                android:textSize="16sp"
                android:textColor="@color/design_default_color_on_surface"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tv_email"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="邮箱: "
                android:textSize="16sp"
                android:textColor="@color/design_default_color_on_surface" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 扫码功能卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        android:layout_marginBottom="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="扫码登录功能"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginBottom="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="点击下方按钮扫描网页端二维码\n完成扫码登录验证"
                android:textSize="14sp"
                android:textColor="@color/design_default_color_on_surface"
                android:gravity="center"
                android:layout_marginBottom="20dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_scan"
                android:layout_width="200dp"
                android:layout_height="56dp"
                android:text="扫一扫"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="28dp"
                app:icon="@android:drawable/ic_menu_camera"
                app:iconGravity="textStart" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 使用说明 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/design_default_color_surface_variant">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="使用说明"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1. 在电脑端打开登录页面\n2. 点击扫一扫按钮\n3. 扫描页面上的二维码\n4. 确认登录信息"
                android:textSize="14sp"
                android:textColor="@color/design_default_color_on_surface"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>