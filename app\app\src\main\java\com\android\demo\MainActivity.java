package com.android.demo;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.android.demo.model.User;
import com.android.demo.utils.PreferenceManager;
import com.android.demo.utils.Logger;
import org.json.JSONObject;

public class MainActivity extends AppCompatActivity {
    
    private TextView tvWelcome, tvUsername, tvEmail;
    private Button btnScan, btnLogout, btnSettings;
    private PreferenceManager preferenceManager;
    private Logger logger;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // 初始化Logger
        logger = Logger.getInstance(this);
        
        // 记录页面访问日志
        logger.pageView("MainActivity");
        
        initViews();
        initServices();
        loadUserInfo();
    }
    
    private void initViews() {
        tvWelcome = findViewById(R.id.tv_welcome);
        tvUsername = findViewById(R.id.tv_username);
        tvEmail = findViewById(R.id.tv_email);
        btnScan = findViewById(R.id.btn_scan);
        btnLogout = findViewById(R.id.btn_logout);
        btnSettings = findViewById(R.id.btn_settings);
        
        logger.debug("MainActivity视图初始化完成");
        
        btnScan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    JSONObject scanData = Logger.createData();
                    scanData.put("action", "用户准备进行扫码操作");
                    logger.userAction("点击扫码按钮", scanData);
                    startScanActivity();
                    logger.info("成功跳转到扫码页面");
                } catch (Exception e) {
                    logger.error("跳转到扫码页面失败", e);
                }
            }
        });
        
        btnLogout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    JSONObject actionData = Logger.createData();
                    actionData.put("action", "用户准备登出");
                    logger.userAction("点击登出按钮", actionData);
                    logout();
                } catch (Exception e) {
                    logger.error("登出操作失败", e);
                }
            }
        });
        
        btnSettings.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    JSONObject actionData = Logger.createData();
                    actionData.put("action", "用户准备进入设置页面");
                    logger.userAction("点击设置按钮", actionData);
                    startSettingsActivity();
                } catch (Exception e) {
                    logger.error("跳转到设置页面失败", e);
                }
            }
        });
    }
    
    private void initServices() {
        preferenceManager = new PreferenceManager(this);
    }
    
    private void loadUserInfo() {
        try {
            if (!preferenceManager.isLoggedIn()) {
                logger.warn("用户未登录，跳转到登录页");
                navigateToLogin();
                return;
            }
            
            User user = preferenceManager.getUser();
            if (user != null) {
                JSONObject userInfo = Logger.createData();
                userInfo.put("username", user.getUsername());
                userInfo.put("email", user.getEmail());
                
                logger.debug("加载用户信息", userInfo);
                
                tvWelcome.setText("欢迎回来！");
                tvUsername.setText("用户名: " + user.getUsername());
                tvEmail.setText("邮箱: " + (user.getEmail() != null ? user.getEmail() : "未设置"));
                
                logger.info("用户信息显示完成");
            } else {
                logger.warn("用户信息为空");
            }
        } catch (Exception e) {
            logger.error("加载用户信息失败", e);
        }
    }
    
    private void startScanActivity() {
        Intent intent = new Intent(this, ScanActivity.class);
        startActivity(intent);
    }
    
    private void startSettingsActivity() {
        Intent intent = new Intent(this, com.example.scanlogin.SettingsActivity.class);
        startActivity(intent);
    }
    
    private void logout() {
        try {
            logger.info("开始执行登出操作");
            
            User user = preferenceManager.getUser();
            if (user != null) {
                JSONObject logoutData = Logger.createData();
                logoutData.put("username", user.getUsername());
                logoutData.put("email", user.getEmail());
                
                logoutData.put("action", "清除本地用户数据并跳转登录页");
                logger.userAction("用户登出", logoutData);
            }
            
            preferenceManager.clearLoginInfo();
            logger.info("本地用户数据已清除");
            
            Toast.makeText(this, "已退出登录", Toast.LENGTH_SHORT).show();
            navigateToLogin();
            
            logger.info("登出操作完成，已跳转到登录页");
        } catch (Exception e) {
            logger.error("登出操作异常", e);
        }
    }
    
    private void navigateToLogin() {
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        try {
            logger.debug("MainActivity onResume - 检查登录状态");
            
            // 检查登录状态
            boolean isLoggedIn = preferenceManager.isLoggedIn();
            
            JSONObject statusData = Logger.createData();
            statusData.put("isLoggedIn", isLoggedIn);
            
            logger.debug("登录状态检查", statusData);
            
            if (!isLoggedIn) {
                logger.warn("用户未登录，跳转到登录页");
                navigateToLogin();
            } else {
                logger.info("用户已登录，继续停留在主页");
            }
        } catch (Exception e) {
            logger.error("检查登录状态时发生异常", e);
        }
    }
}