package com.android.demo;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.android.demo.network.ApiClient;
import com.android.demo.network.ApiService;
import com.android.demo.model.LoginRequest;
import com.android.demo.model.LoginResponse;
import com.android.demo.utils.PreferenceManager;
import com.android.demo.utils.Logger;
import org.json.JSONObject;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class LoginActivity extends AppCompatActivity {
    
    private EditText etUsername, etPassword;
    private Button btnLogin;
    private ProgressBar progressBar;
    private ApiService apiService;
    private PreferenceManager preferenceManager;
    private Logger logger;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        
        // 初始化Logger
        logger = Logger.getInstance(this);
        
        // 初始化ApiClient
        ApiClient.init(this);
        
        // 记录页面访问日志
        logger.pageView("LoginActivity");
        
        initViews();
        initServices();
        checkLoginStatus();
    }
    
    private void initViews() {
        etUsername = findViewById(R.id.et_username);
        etPassword = findViewById(R.id.et_password);
        btnLogin = findViewById(R.id.btn_login);
        progressBar = findViewById(R.id.progress_bar);
        
        logger.debug("LoginActivity视图初始化完成");
        
        btnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    JSONObject loginData = Logger.createData();
                    loginData.put("action", "用户准备进行登录");
                    logger.userAction("点击登录按钮", loginData);
                    attemptLogin();
                } catch (Exception e) {
                    logger.error("登录操作失败", e);
                }
            }
        });
    }
    
    private void initServices() {
        try {
            apiService = ApiClient.getClient().create(ApiService.class);
            preferenceManager = new PreferenceManager(this);
            logger.debug("服务初始化完成");
        } catch (Exception e) {
            logger.error("服务初始化失败", e);
        }
    }
    
    private void checkLoginStatus() {
        try {
            boolean isLoggedIn = preferenceManager.isLoggedIn();
            
            JSONObject statusData = Logger.createData();
            statusData.put("isLoggedIn", isLoggedIn);
            
            logger.debug("检查登录状态", statusData);
            
            if (isLoggedIn) {
                logger.info("用户已登录，跳转到主页");
                navigateToMain();
            } else {
                logger.info("用户未登录，停留在登录页");
            }
        } catch (Exception e) {
            logger.error("检查登录状态时发生异常", e);
        }
    }
    
    private void attemptLogin() {
        try {
            String username = etUsername.getText().toString().trim();
            String password = etPassword.getText().toString().trim();
            
            JSONObject loginData = Logger.createData();
            loginData.put("username", username);
            loginData.put("hasPassword", !TextUtils.isEmpty(password));
            
            logger.info("开始尝试登录", loginData);
            
            if (TextUtils.isEmpty(username)) {
                logger.warn("用户名为空");
                etUsername.setError("请输入用户名");
                etUsername.requestFocus();
                return;
            }
            
            if (TextUtils.isEmpty(password)) {
                logger.warn("密码为空");
                etPassword.setError("请输入密码");
                etPassword.requestFocus();
                return;
            }
            
            logger.debug("表单验证通过，开始登录请求");
            showLoading(true);
            
            LoginRequest request = new LoginRequest(username, password);
            Call<LoginResponse> call = apiService.login(request);
            
            logger.info("发送登录API请求");
            
            call.enqueue(new Callback<LoginResponse>() {
                @Override
                public void onResponse(Call<LoginResponse> call, Response<LoginResponse> response) {
                    try {
                        showLoading(false);
                        
                        JSONObject responseData = Logger.createData();
                        responseData.put("username", username);
                        responseData.put("httpStatus", response.code());
                        responseData.put("isSuccessful", response.isSuccessful());
                        
                        if (response.isSuccessful() && response.body() != null) {
                            LoginResponse loginResponse = response.body();
                            responseData.put("apiSuccess", loginResponse.isSuccess());
                            responseData.put("message", loginResponse.getMessage());
                            
                            logger.debug("登录API响应", responseData);
                            
                            if (loginResponse.isSuccess()) {
                                // 保存登录信息
                                preferenceManager.saveLoginInfo(
                                    loginResponse.getToken(),
                                    loginResponse.getUser()
                                );
                                
                                JSONObject successData = Logger.createData();
                                successData.put("username", username);
                                successData.put("userId", loginResponse.getUser() != null ? loginResponse.getUser().getId() : null);
                                
                                logger.info("登录成功", successData);
                                logger.userAction("登录成功", successData);
                                
                                Toast.makeText(LoginActivity.this, "登录成功", Toast.LENGTH_SHORT).show();
                                navigateToMain();
                            } else {
                                logger.warn("登录失败: " + loginResponse.getMessage(), responseData);
                                Toast.makeText(LoginActivity.this, loginResponse.getMessage(), Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            logger.error("登录HTTP请求失败，状态码: " + response.code(), null, responseData);
                            Toast.makeText(LoginActivity.this, "登录失败，请重试", Toast.LENGTH_SHORT).show();
                        }
                    } catch (Exception e) {
                        logger.error("处理登录响应时发生异常", e);
                        showLoading(false);
                    }
                }
                
                @Override
                public void onFailure(Call<LoginResponse> call, Throwable t) {
                    try {
                        showLoading(false);
                        
                        JSONObject errorData = Logger.createData();
                        errorData.put("username", username);
                        errorData.put("errorMessage", t.getMessage());
                        
                        logger.error("登录网络错误", t);
                        Toast.makeText(LoginActivity.this, "网络错误：" + t.getMessage(), Toast.LENGTH_SHORT).show();
                    } catch (Exception e) {
                        logger.error("处理登录网络错误时发生异常", e);
                    }
                }
            });
        } catch (Exception e) {
            logger.error("登录过程中发生异常", e);
            showLoading(false);
        }
    }
    
    private void showLoading(boolean show) {
        try {
            JSONObject loadingData = Logger.createData();
            loadingData.put("show", show);
            
            logger.debug("切换加载状态", loadingData);
            
            if (show) {
                progressBar.setVisibility(View.VISIBLE);
                btnLogin.setEnabled(false);
                logger.debug("显示加载状态，禁用登录按钮");
            } else {
                progressBar.setVisibility(View.GONE);
                btnLogin.setEnabled(true);
                logger.debug("隐藏加载状态，启用登录按钮");
            }
        } catch (Exception e) {
            logger.error("切换加载状态时发生异常", e);
        }
    }
    
    private void navigateToMain() {
        try {
            logger.info("跳转到主页");
            Intent intent = new Intent(this, MainActivity.class);
            startActivity(intent);
            finish();
            logger.debug("已跳转到主页并结束登录页");
        } catch (Exception e) {
            logger.error("跳转到主页时发生异常", e);
        }
    }
}