# Android Studio 编译错误修复方案

## 问题分析

根据 `bug.md` 中的错误信息，主要问题包括：

### 1. Android Studio 调试器兼容性问题
```
Could not compile initialization script '/private/var/folders/.../ijJvmDebugger1.gradle'
unable to resolve class com.intellij.gradle.toolingExtension.impl.initScript.util.GradleJvmForkedDebuggerHelper
```

### 2. Gradle 缓存损坏问题
```
Could not read workspace metadata from /Users/<USER>/.gradle/caches/8.13/kotlin-dsl/accessors/.../metadata.bin
```

### 3. 网络连接问题
```
java.net.SocketTimeoutException: Read timed out
```

## 解决方案

### 方案一：Android Studio 设置修复（推荐）

1. **禁用 Android Studio 的 Gradle 调试器**
   - 打开 Android Studio
   - 进入 `Preferences` > `Build, Execution, Deployment` > `Gradle`
   - 取消勾选 "Use Gradle from: 'gradle-wrapper.properties' file"
   - 选择 "Use local gradle distribution"
   - 或者在 "Gradle JVM" 中选择正确的 JDK 版本（Java 17）

2. **更新 Android Studio Gradle 插件设置**
   - 进入 `Preferences` > `Build, Execution, Deployment` > `Gradle`
   - 确保 "Gradle JVM" 设置为 Java 17
   - 路径应该是：`/Users/<USER>/Library/Java/JavaVirtualMachines/jbr-17.0.9/Contents/Home`

3. **清理并重新同步项目**
   - 在 Android Studio 中选择 `Build` > `Clean Project`
   - 然后选择 `File` > `Sync Project with Gradle Files`

### 方案二：命令行修复

1. **完全清理 Gradle 缓存**
```bash
# 清理项目本地缓存
rm -rf .gradle
rm -rf build
rm -rf app/build

# 清理全局 Gradle 缓存
rm -rf ~/.gradle/caches
rm -rf ~/.gradle/wrapper
```

2. **重新下载 Gradle Wrapper**
```bash
# 如果网络有问题，可以手动下载 Gradle
wget https://services.gradle.org/distributions/gradle-8.13-bin.zip
mkdir -p ~/.gradle/wrapper/dists/gradle-8.13-bin/
unzip gradle-8.13-bin.zip -d ~/.gradle/wrapper/dists/gradle-8.13-bin/
```

3. **使用本地 Gradle 构建**
```bash
# 安装 Gradle（如果没有安装）
brew install gradle

# 使用本地 Gradle 构建
gradle clean build
```

### 方案三：配置文件修复

1. **更新 gradle.properties**
```properties
# 添加网络配置
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# 网络代理设置（如果需要）
# systemProp.http.proxyHost=proxy.company.com
# systemProp.http.proxyPort=8080
# systemProp.https.proxyHost=proxy.company.com
# systemProp.https.proxyPort=8080
```

2. **检查 JDK 配置**
```bash
# 验证 Java 版本
java -version

# 验证 JAVA_HOME
echo $JAVA_HOME

# 如果需要，设置正确的 JAVA_HOME
export JAVA_HOME=/Users/<USER>/Library/Java/JavaVirtualMachines/jbr-17.0.9/Contents/Home
```

## 验证修复

1. **命令行验证**
```bash
./gradlew clean
./gradlew assembleDebug
```

2. **Android Studio 验证**
   - 重新打开项目
   - 等待 Gradle 同步完成
   - 尝试构建项目

## 预防措施

1. **定期清理缓存**
   - 每周清理一次 Gradle 缓存
   - 避免在网络不稳定时进行大型构建

2. **保持版本兼容性**
   - 定期更新 Android Studio
   - 保持 Gradle 和 AGP 版本同步
   - 使用稳定版本的依赖库

3. **备份配置**
   - 备份 `gradle.properties` 和构建脚本
   - 记录工作的配置组合

## 技术细节

- **Android Studio 版本**: Narwhal Feature Drop 2025.1.2
- **Gradle 版本**: 8.13
- **AGP 版本**: 8.2.2
- **Java 版本**: 17
- **操作系统**: macOS 15.6

这些解决方案应该能够解决 Android Studio 的编译错误问题。建议按照方案一开始，如果仍有问题再尝试其他方案。