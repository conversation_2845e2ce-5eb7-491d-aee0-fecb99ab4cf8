<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:layout_marginBottom="8dp"
    app:cardBackgroundColor="@color/design_default_color_surface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <!-- URL信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="12dp">

            <TextView
                android:id="@+id/tv_url"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="http://********:3001/api/"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/tv_last_used"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="最后使用: 2024-01-01 12:00"
                android:textSize="12sp"
                android:textColor="@color/design_default_color_on_surface"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_use"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="使用"
                android:textSize="12sp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="8dp"
                android:layout_marginEnd="4dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="删除"
                android:textSize="12sp"
                android:textColor="@color/design_default_color_error"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:padding="8dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>