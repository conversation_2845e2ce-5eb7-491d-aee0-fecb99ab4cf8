package com.android.demo.model;

import com.google.gson.annotations.SerializedName;

public class LoginResponse {
    
    @SerializedName("success")
    private boolean success;
    
    @SerializedName("message")
    private String message;
    
    @SerializedName("token")
    private String token;
    
    @SerializedName("user")
    private User user;
    
    public LoginResponse() {}
    
    public LoginResponse(boolean success, String message, String token, User user) {
        this.success = success;
        this.message = message;
        this.token = token;
        this.user = user;
    }
    
    // Getters
    public boolean isSuccess() {
        return success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public String getToken() {
        return token;
    }
    
    public User getUser() {
        return user;
    }
    
    // Setters
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
}