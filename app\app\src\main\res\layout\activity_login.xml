<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/white">

    <!-- Logo或标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="扫码登录"
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="48dp" />

    <!-- 登录表单 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp"
        android:layout_marginBottom="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 用户名输入框 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:boxStrokeColor="@color/design_default_color_primary"
                app:hintTextColor="@color/design_default_color_primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_username"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="用户名"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 密码输入框 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:boxStrokeColor="@color/design_default_color_primary"
                app:hintTextColor="@color/design_default_color_primary"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="密码"
                    android:inputType="textPassword"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 登录按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_login"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="登录"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="8dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <!-- 提示文本 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="测试账号: admin / 123456"
        android:textSize="14sp"
        android:textColor="@color/design_default_color_secondary"
        android:layout_marginTop="16dp" />

</LinearLayout>