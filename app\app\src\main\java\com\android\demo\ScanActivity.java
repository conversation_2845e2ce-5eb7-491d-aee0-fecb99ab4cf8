package com.android.demo;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.android.demo.network.ApiClient;
import com.android.demo.network.ApiService;
import com.android.demo.model.QrConfirmRequest;
import com.android.demo.model.ApiResponse;
import com.android.demo.utils.PreferenceManager;
import com.android.demo.utils.Logger;
import com.journeyapps.barcodescanner.CaptureManager;
import com.journeyapps.barcodescanner.DecoratedBarcodeView;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import org.json.JSONObject;

public class ScanActivity extends AppCompatActivity {
    
    private static final int CAMERA_PERMISSION_REQUEST = 100;
    
    private DecoratedBarcodeView barcodeView;
    private CaptureManager captureManager;
    private ApiService apiService;
    private PreferenceManager preferenceManager;
    private boolean isScanning = true;
    private Logger logger;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_scan);
        
        // 初始化Logger
        logger = Logger.getInstance(this);
        
        // 记录页面访问日志
        logger.pageView("ScanActivity");
        
        initViews();
        initServices();
        checkCameraPermission();
    }
    
    private void initViews() {
        try {
            logger.debug("初始化扫码视图");
            barcodeView = findViewById(R.id.barcode_scanner);
            
            // 设置扫码回调
            barcodeView.decodeContinuous(new BarcodeCallback() {
                @Override
                public void barcodeResult(BarcodeResult result) {
                    try {
                        if (isScanning && result.getText() != null) {
                            isScanning = false;
                            
                            String qrContent = result.getText();
                            
                            JSONObject scanData = Logger.createData();
                            scanData.put("qrContent", qrContent);
                            scanData.put("format", result.getBarcodeFormat().toString());
                            
                            scanData.put("message", "扫描到二维码");
                logger.scanEvent("QR_CODE_SCANNED", scanData);
                            
                            handleScanResult(qrContent);
                        }
                    } catch (Exception e) {
                        logger.error("处理扫码结果时发生异常", e);
                        resumeScanning();
                    }
                }
            });
            logger.info("扫码视图初始化完成");
        } catch (Exception e) {
            logger.error("初始化扫码视图失败", e);
        }
    }
    
    private void initServices() {
        apiService = ApiClient.getClient().create(ApiService.class);
        preferenceManager = new PreferenceManager(this);
    }
    
    private void checkCameraPermission() {
        logger.debug("检查相机权限");
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
                != PackageManager.PERMISSION_GRANTED) {
            logger.info("相机权限未授予，请求权限");
            logger.info("请求相机权限用于扫码");
            ActivityCompat.requestPermissions(this, 
                new String[]{Manifest.permission.CAMERA}, 
                CAMERA_PERMISSION_REQUEST);
        } else {
            logger.info("相机权限已授予，初始化相机");
            logger.permissionEvent("CAMERA", true);
            initCamera();
        }
    }
    
    private void initCamera() {
        try {
            logger.debug("初始化相机");
            captureManager = new CaptureManager(this, barcodeView);
            captureManager.initializeFromIntent(getIntent(), null);
            captureManager.decode();
            logger.info("相机初始化完成");
        } catch (Exception e) {
            logger.error("初始化相机失败", e);
        }
    }
    
    private void handleScanResult(String qrCode) {
        try {
            logger.info("开始处理扫码结果: " + qrCode);
            
            JSONObject scanData = Logger.createData();
            scanData.put("qrCode", qrCode);
            
            logger.debug("解析二维码内容", scanData);
            
            // 解析二维码内容，期望格式: http://localhost:5173/login?qr=uuid
            String qrId = extractQrId(qrCode);
            if (qrId != null) {
                JSONObject parseData = Logger.createData();
                parseData.put("qrId", qrId);
                
                logger.info("成功解析到qrId", parseData);
                parseData.put("message", "二维码解析成功");
            logger.scanEvent("QR_PARSED", parseData);
                
                confirmQrCode(qrId);
            } else {
                logger.warn("二维码格式无效");
                scanData.put("message", "无效的二维码格式");
            logger.scanEvent("QR_INVALID", scanData);
                Toast.makeText(this, "无效的二维码", Toast.LENGTH_SHORT).show();
                resumeScanning();
            }
        } catch (Exception e) {
            logger.error("处理扫码结果时发生异常", e);
            resumeScanning();
        }
    }
    
    private String extractQrId(String qrCode) {
        try {
            logger.debug("提取二维码ID: " + qrCode);
            // 从URL中提取qr参数
            if (qrCode.contains("qr=")) {
                String[] parts = qrCode.split("qr=");
                if (parts.length > 1) {
                    String qrId = parts[1].split("&")[0]; // 获取qr参数值
                    logger.debug("成功提取qrId: " + qrId);
                    return qrId;
                }
            }
            logger.warn("未找到qr参数");
        } catch (Exception e) {
            logger.error("提取二维码ID时发生异常", e);
        }
        return null;
    }
    
    private void confirmQrCode(String qrId) {
        try {
            logger.info("开始确认二维码，qrId: " + qrId);
            
            String token = preferenceManager.getToken();
            
            JSONObject confirmData = Logger.createData();
            confirmData.put("qrId", qrId);
            confirmData.put("hasToken", token != null);
            
            logger.debug("二维码确认准备数据", confirmData);
            
            if (token == null) {
                logger.warn("用户未登录，无法确认二维码");
                confirmData.put("message", "用户未登录");
                logger.scanEvent("QR_CONFIRM_FAILED", confirmData);
                Toast.makeText(this, "请先登录", Toast.LENGTH_SHORT).show();
                finish();
                return;
            }
            
            QrConfirmRequest request = new QrConfirmRequest(qrId, token);
            Call<ApiResponse> call = apiService.confirmQrCode(request);
            
            logger.info("发送二维码确认请求");
            confirmData.put("message", "开始确认二维码");
            logger.scanEvent("QR_CONFIRM_START", confirmData);
            
            call.enqueue(new Callback<ApiResponse>() {
                @Override
                public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                    try {
                        JSONObject responseData = Logger.createData();
                        responseData.put("qrId", qrId);
                        responseData.put("httpStatus", response.code());
                        responseData.put("isSuccessful", response.isSuccessful());
                        
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse apiResponse = response.body();
                            responseData.put("apiSuccess", apiResponse.isSuccess());
                            responseData.put("message", apiResponse.getMessage());
                            
                            logger.debug("二维码确认API响应", responseData);
                            
                            if (apiResponse.isSuccess()) {
                                logger.info("二维码确认成功");
                                responseData.put("message", "二维码确认成功");
                        logger.scanEvent("QR_CONFIRM_SUCCESS", responseData);
                                Toast.makeText(ScanActivity.this, "扫码登录确认成功", Toast.LENGTH_SHORT).show();
                                finish();
                            } else {
                                logger.warn("二维码确认失败: " + apiResponse.getMessage());
                                responseData.put("message", "API返回失败");
                            logger.scanEvent("QR_CONFIRM_FAILED", responseData);
                                Toast.makeText(ScanActivity.this, apiResponse.getMessage(), Toast.LENGTH_SHORT).show();
                                resumeScanning();
                            }
                        } else {
                            logger.error("二维码确认网络请求失败，HTTP状态: " + response.code());
                            responseData.put("message", "HTTP请求失败");
                        logger.scanEvent("QR_CONFIRM_HTTP_ERROR", responseData);
                            Toast.makeText(ScanActivity.this, "确认失败，请重试", Toast.LENGTH_SHORT).show();
                            resumeScanning();
                        }
                    } catch (Exception e) {
                        logger.error("处理二维码确认响应时发生异常", e);
                        resumeScanning();
                    }
                }
                
                @Override
                public void onFailure(Call<ApiResponse> call, Throwable t) {
                    try {
                        JSONObject errorData = Logger.createData();
                        errorData.put("qrId", qrId);
                        errorData.put("errorMessage", t.getMessage());
                        
                        logger.error("二维码确认网络错误", t);
                        errorData.put("message", "网络连接失败");
                    logger.scanEvent("QR_CONFIRM_NETWORK_ERROR", errorData);
                        Toast.makeText(ScanActivity.this, "网络错误：" + t.getMessage(), Toast.LENGTH_SHORT).show();
                        resumeScanning();
                    } catch (Exception e) {
                        logger.error("处理网络错误时发生异常", e);
                        resumeScanning();
                    }
                }
            });
        } catch (Exception e) {
            logger.error("确认二维码过程中发生异常", e);
            resumeScanning();
        }
    }
    
    private void resumeScanning() {
        try {
            logger.debug("恢复扫码");
            isScanning = true;
            if (captureManager != null) {
                captureManager.decode();
                logger.debug("扫码已恢复");
            }
        } catch (Exception e) {
            logger.error("恢复扫码时发生异常", e);
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == CAMERA_PERMISSION_REQUEST) {
            try {
                JSONObject permissionData = Logger.createData();
                permissionData.put("requestCode", requestCode);
                permissionData.put("permissions", java.util.Arrays.toString(permissions));
                permissionData.put("grantResults", java.util.Arrays.toString(grantResults));
                
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    logger.info("相机权限授予成功");
                    logger.permissionEvent("CAMERA", true);
                    initCamera();
                } else {
                    logger.warn("相机权限被拒绝");
                    logger.permissionEvent("CAMERA", false);
                    Toast.makeText(this, "需要摄像头权限才能扫码", Toast.LENGTH_SHORT).show();
                    finish();
                }
            } catch (Exception e) {
                logger.error("处理权限请求结果时发生异常", e);
                finish();
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        try {
            logger.debug("ScanActivity onResume");
            if (captureManager != null) {
                captureManager.onResume();
                logger.debug("扫码管理器已恢复");
            }
        } catch (Exception e) {
            logger.error("恢复扫码管理器时发生异常", e);
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        try {
            logger.debug("ScanActivity onPause");
            if (captureManager != null) {
                captureManager.onPause();
                logger.debug("扫码管理器已暂停");
            }
        } catch (Exception e) {
            logger.error("暂停扫码管理器时发生异常", e);
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        try {
            logger.debug("ScanActivity onDestroy");
            if (captureManager != null) {
                captureManager.onDestroy();
                logger.debug("扫码管理器已销毁");
            }
        } catch (Exception e) {
            logger.error("销毁扫码管理器时发生异常", e);
        }
    }
}